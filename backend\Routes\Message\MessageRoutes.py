from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Message.MessageControllers import MessageControllers
from ...DataBase.DataBase import get_db
from ...Schemas.MessageSchemas.MessageSchemas import Message, MessageCreate, MessageUpdate

Message_Router = APIRouter(
    prefix="/messages",
    tags=["messages"],
)

# Create Message
@Message_Router.post("/", response_model=Message)
async def create_message_route(message: MessageCreate, db: Session = Depends(get_db)):
    return await MessageControllers.create_message(db, message)

# Get All Messages
@Message_Router.get("/", response_model=List[Message])
async def read_messages_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await MessageControllers.get_messages(db, skip, limit)

# Get Single Message
@Message_Router.get("/{message_id}", response_model=Message)
async def read_message_route(message_id: int, db: Session = Depends(get_db)):
    return await MessageControllers.get_message(db, message_id)

# Update Message
@Message_Router.put("/{message_id}", response_model=Message)
async def update_message_route(message_id: int, message: MessageUpdate, db: Session = Depends(get_db)):
    return await MessageControllers.update_message(db, message_id, message)

# Delete Message
@Message_Router.delete("/{message_id}", response_model=Message)
async def delete_message_route(message_id: int, db: Session = Depends(get_db)):
    return await MessageControllers.delete_message(db, message_id)
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.User.CommentControllers import CommentControllers
from ...DataBase.DataBase import get_db
from ...Schemas.UserSchemas.CommentSchemas import Comment, CommentCreate, CommentUpdate

Comment_Router = APIRouter(
    prefix="/comments",
    tags=["comments"],
)

# Create Comment
@Comment_Router.post("/", response_model=Comment)
async def create_comment_route(comment: CommentCreate, db: Session = Depends(get_db)):
    return await CommentControllers.create_comment(db, comment)

# Get All Comments
@Comment_Router.get("/", response_model=List[Comment])
async def read_comments_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await CommentControllers.get_comments(db, skip, limit)

# Get Single Comment
@Comment_Router.get("/{comment_id}", response_model=Comment)
async def read_comment_route(comment_id: int, db: Session = Depends(get_db)):
    return await CommentControllers.get_comment(db, comment_id)

# Update Comment
@Comment_Router.put("/{comment_id}", response_model=Comment)
async def update_comment_route(comment_id: int, comment: CommentUpdate, db: Session = Depends(get_db)):
    return await CommentControllers.update_comment(db, comment_id, comment)

# Delete Comment
@Comment_Router.delete("/{comment_id}", response_model=Comment)
async def delete_comment_route(comment_id: int, db: Session = Depends(get_db)):
    return await CommentControllers.delete_comment(db, comment_id)

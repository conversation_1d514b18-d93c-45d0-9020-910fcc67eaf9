from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from ...Models.Order.OrderModel import OrderStatus
from .OrderItemSchemas import OrderItem

class OrderBase(BaseModel):
    total_price: float
    status: OrderStatus = OrderStatus.PENDING

class OrderCreate(OrderBase):
    user_id: int
    items: List[int] = []

class OrderUpdate(BaseModel):
    status: Optional[OrderStatus] = None

class Order(OrderBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    items: List[OrderItem] = []

    class Config:
        from_attributes = True

from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Table
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

# Association Table for Doner and Ingredient
doner_ingredients = Table('doner_ingredients', Base.metadata,
    Column('doner_id', Integer, ForeignKey('doners.id'), primary_key=True),
    Column('ingredient_id', Integer, ForeignKey('ingredients.id'), primary_key=True)
)

class Doner(Base):
    __tablename__ = 'doners'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)
    meat_type = Column(String(50))
    style = Column(String(50)) # e.g., Wrap, Plate
    image_url = Column(String(255))
    is_available = Column(Boolean, default=True)

    category_id = Column(Integer, ForeignKey('categories.id'))
    category = relationship("Category", back_populates="doners")

    # Many-to-many relationship with Ingredient
    ingredients = relationship("Ingredient", secondary=doner_ingredients, back_populates="doners")

    # Relationship to menus (one doner can be in multiple menus)
    menus = relationship("Menu", foreign_keys="Menu.doner_id", back_populates="doner")

    def __repr__(self):
        return f"<Doner(id={self.id}, name='{self.name}')>"

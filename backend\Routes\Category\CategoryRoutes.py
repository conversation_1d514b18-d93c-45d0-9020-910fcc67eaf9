from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Category.CategoryControllers import CategoryControllers
from ...DataBase.DataBase import get_db
from ...Schemas.CategorySchemas.CategorySchemas import Category, CategoryCreate, CategoryUpdate

Category_Router = APIRouter(
    prefix="/categories",
    tags=["categories"],
)

# Create Category
@Category_Router.post("/", response_model=Category)
async def create_category_route(category: CategoryCreate, db: Session = Depends(get_db)):
    return await CategoryControllers.create_category(db, category)

# Get All Categories
@Category_Router.get("/", response_model=List[Category])
async def read_categories_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await CategoryControllers.get_categories(db, skip, limit)

# Get Single Category
@Category_Router.get("/{category_id}", response_model=Category)
async def read_category_route(category_id: int, db: Session = Depends(get_db)):
    return await CategoryControllers.get_category(db, category_id)

# Update Category
@Category_Router.put("/{category_id}", response_model=Category)
async def update_category_route(category_id: int, category: CategoryUpdate, db: Session = Depends(get_db)):
    return await CategoryControllers.update_category(db, category_id, category)

# Delete Category
@Category_Router.delete("/{category_id}", response_model=Category)
async def delete_category_route(category_id: int, db: Session = Depends(get_db)):
    return await CategoryControllers.delete_category(db, category_id)
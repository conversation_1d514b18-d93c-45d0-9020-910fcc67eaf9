from pydantic import BaseModel
from typing import Optional

# Base schema for address attributes
class AddressBase(BaseModel):
    address_line_1: str
    address_line_2: Optional[str] = None
    city: str
    state: str
    postal_code: str
    country: str

# Schema for creating a new address
class AddressCreate(AddressBase):
    user_id: int

# Schema for updating an address
class AddressUpdate(BaseModel):
    address_line_1: Optional[str] = None
    address_line_2: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

# Schema for representing an address in the database
class Address(AddressBase):
    id: int
    user_id: int

    class Config:
        from_attributes = True

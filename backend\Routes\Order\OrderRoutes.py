from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Order.OrderControllers import OrderControllers
from ...DataBase.DataBase import get_db
from ...Schemas.OrderSchemas.OrderSchemas import Order, OrderCreate, OrderUpdate

Order_Router = APIRouter(
    prefix="/orders",
    tags=["orders"],
)

# Create Order
@Order_Router.post("/", response_model=Order)
async def create_order_route(order: OrderCreate, db: Session = Depends(get_db)):
    return await OrderControllers.create_order(db, order)

# Get All Orders
@Order_Router.get("/", response_model=List[Order])
async def read_orders_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await OrderControllers.get_orders(db, skip, limit)

# Get Single Order
@Order_Router.get("/{order_id}", response_model=Order)
async def read_order_route(order_id: int, db: Session = Depends(get_db)):
    return await OrderControllers.get_order(db, order_id)

# Update Order
@Order_Router.put("/{order_id}", response_model=Order)
async def update_order_route(order_id: int, order: OrderUpdate, db: Session = Depends(get_db)):
    return await OrderControllers.update_order(db, order_id, order)

# Delete Order
@Order_Router.delete("/{order_id}", response_model=Order)
async def delete_order_route(order_id: int, db: Session = Depends(get_db)):
    return await OrderControllers.delete_order(db, order_id)
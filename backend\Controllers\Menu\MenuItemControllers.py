from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Menu.MenuItemModel import MenuItem as MenuItemModel
from ...Schemas.MenuSchemas.MenuItemSchemas import MenuItem, MenuItemCreate, MenuItemUpdate


class MenuItemControllers:
    
    @staticmethod
    async def get_menu_item(db: Session, menu_item_id: int) -> MenuItemModel:
        """Get a single menu item by ID"""
        menu_item = db.query(MenuItemModel).filter(MenuItemModel.id == menu_item_id).first()
        if not menu_item:
            raise HTTPException(status_code=404, detail="Menu item not found")
        return menu_item
    
    @staticmethod
    async def get_menu_items(db: Session, skip: int = 0, limit: int = 100) -> List[MenuItemModel]:
        """Get all menu items with pagination"""
        return db.query(MenuItemModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_menu_item(db: Session, menu_item: MenuItemCreate) -> MenuItemModel:
        """Create a new menu item"""
        db_menu_item = MenuItemModel(**menu_item.model_dump())
        db.add(db_menu_item)
        db.commit()
        db.refresh(db_menu_item)
        return db_menu_item
    
    @staticmethod
    async def update_menu_item(db: Session, menu_item_id: int, menu_item: MenuItemUpdate) -> MenuItemModel:
        """Update an existing menu item"""
        db_menu_item = db.query(MenuItemModel).filter(MenuItemModel.id == menu_item_id).first()
        if not db_menu_item:
            raise HTTPException(status_code=404, detail="Menu item not found")
        
        update_data = menu_item.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_menu_item, key, value)
        
        db.commit()
        db.refresh(db_menu_item)
        return db_menu_item
    
    @staticmethod
    async def delete_menu_item(db: Session, menu_item_id: int) -> MenuItemModel:
        """Delete a menu item"""
        db_menu_item = db.query(MenuItemModel).filter(MenuItemModel.id == menu_item_id).first()
        if not db_menu_item:
            raise HTTPException(status_code=404, detail="Menu item not found")
        
        db.delete(db_menu_item)
        db.commit()
        return db_menu_item

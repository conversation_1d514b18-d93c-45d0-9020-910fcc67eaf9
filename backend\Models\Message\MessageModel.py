from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Foreign<PERSON>ey, func
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class Message(Base):
    __tablename__ = 'messages'

    id = Column(Integer, primary_key=True, index=True)
    subject = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=func.now())
    is_read = Column(Boolean, default=False, nullable=False)

    # Foreign Keys
    sender_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    recipient_id = Column(Integer, ForeignKey('users.id'), nullable=False)

    # Relationships
    sender = relationship("User", foreign_keys=[sender_id], back_populates="sent_messages")
    recipient = relationship("User", foreign_keys=[recipient_id], back_populates="received_messages")

    def __repr__(self):
        return f"<Message(id={self.id}, subject='{self.subject}', from={self.sender_id}, to={self.recipient_id})>"

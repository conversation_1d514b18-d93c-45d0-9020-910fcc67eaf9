from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Category.CategoryModel import Category as CategoryModel
from ...Schemas.CategorySchemas.CategorySchemas import Category, CategoryCreate, CategoryUpdate


class CategoryControllers:
    
    @staticmethod
    async def get_category(db: Session, category_id: int) -> CategoryModel:
        """Get a single category by ID"""
        category = db.query(CategoryModel).filter(CategoryModel.id == category_id).first()
        if not category:
            raise HTTPException(status_code=404, detail="Category not found")
        return category
    
    @staticmethod
    async def get_categories(db: Session, skip: int = 0, limit: int = 100) -> List[CategoryModel]:
        """Get all categories with pagination"""
        return db.query(CategoryModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_category(db: Session, category: CategoryCreate) -> CategoryModel:
        """Create a new category"""
        db_category = CategoryModel(**category.model_dump())
        db.add(db_category)
        db.commit()
        db.refresh(db_category)
        return db_category
    
    @staticmethod
    async def update_category(db: Session, category_id: int, category: CategoryUpdate) -> CategoryModel:
        """Update an existing category"""
        db_category = db.query(CategoryModel).filter(CategoryModel.id == category_id).first()
        if not db_category:
            raise HTTPException(status_code=404, detail="Category not found")
        
        update_data = category.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_category, key, value)
        
        db.commit()
        db.refresh(db_category)
        return db_category
    
    @staticmethod
    async def delete_category(db: Session, category_id: int) -> CategoryModel:
        """Delete a category"""
        db_category = db.query(CategoryModel).filter(CategoryModel.id == category_id).first()
        if not db_category:
            raise HTTPException(status_code=404, detail="Category not found")
        
        db.delete(db_category)
        db.commit()
        return db_category

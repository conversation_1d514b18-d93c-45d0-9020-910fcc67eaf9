from fastapi import HTTP<PERSON>x<PERSON>
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.User.AddressModel import Address as AddressModel
from ...Schemas.UserSchemas.AddressSchemas import Address, AddressCreate, AddressUpdate


class AddressControllers:
    
    @staticmethod
    async def get_address(db: Session, address_id: int) -> Optional[AddressModel]:
        """Get a single address by ID"""
        address = db.query(AddressModel).filter(AddressModel.id == address_id).first()
        if not address:
            raise HTTPException(status_code=404, detail="Address not found")
        return address
    
    @staticmethod
    async def get_addresses(db: Session, skip: int = 0, limit: int = 100) -> List[AddressModel]:
        """Get all addresses with pagination"""
        return db.query(AddressModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_address(db: Session, address: AddressCreate) -> AddressModel:
        """Create a new address"""
        db_address = AddressModel(**address.model_dump())
        db.add(db_address)
        db.commit()
        db.refresh(db_address)
        return db_address
    
    @staticmethod
    async def update_address(db: Session, address_id: int, address: AddressUpdate) -> AddressModel:
        """Update an existing address"""
        db_address = db.query(AddressModel).filter(AddressModel.id == address_id).first()
        if not db_address:
            raise HTTPException(status_code=404, detail="Address not found")
        
        update_data = address.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_address, key, value)
        
        db.commit()
        db.refresh(db_address)
        return db_address
    
    @staticmethod
    async def delete_address(db: Session, address_id: int) -> AddressModel:
        """Delete an address"""
        db_address = db.query(AddressModel).filter(AddressModel.id == address_id).first()
        if not db_address:
            raise HTTPException(status_code=404, detail="Address not found")
        
        db.delete(db_address)
        db.commit()
        return db_address

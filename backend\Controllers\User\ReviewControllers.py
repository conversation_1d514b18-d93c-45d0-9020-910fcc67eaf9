from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.User.ReviewModel import Review as ReviewModel
from ...Schemas.UserSchemas.ReviewSchemas import Review, ReviewCreate, ReviewUpdate


class ReviewControllers:
    
    @staticmethod
    async def get_review(db: Session, review_id: int) -> Optional[ReviewModel]:
        """Get a single review by ID"""
        review = db.query(ReviewModel).filter(ReviewModel.id == review_id).first()
        if not review:
            raise HTTPException(status_code=404, detail="Review not found")
        return review
    
    @staticmethod
    async def get_reviews(db: Session, skip: int = 0, limit: int = 100) -> List[ReviewModel]:
        """Get all reviews with pagination"""
        return db.query(ReviewModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_review(db: Session, review: ReviewCreate) -> ReviewModel:
        """Create a new review"""
        db_review = ReviewModel(**review.model_dump())
        db.add(db_review)
        db.commit()
        db.refresh(db_review)
        return db_review
    
    @staticmethod
    async def update_review(db: Session, review_id: int, review: ReviewUpdate) -> ReviewModel:
        """Update an existing review"""
        db_review = db.query(ReviewModel).filter(ReviewModel.id == review_id).first()
        if not db_review:
            raise HTTPException(status_code=404, detail="Review not found")
        
        update_data = review.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_review, key, value)
        
        db.commit()
        db.refresh(db_review)
        return db_review
    
    @staticmethod
    async def delete_review(db: Session, review_id: int) -> ReviewModel:
        """Delete a review"""
        db_review = db.query(ReviewModel).filter(ReviewModel.id == review_id).first()
        if not db_review:
            raise HTTPException(status_code=404, detail="Review not found")
        
        db.delete(db_review)
        db.commit()
        return db_review

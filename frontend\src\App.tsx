import { BrowserRouter, Routes, Route } from "react-router-dom";
import Home from "./Pages/Home/Home";
import Header from "./Components/Header/Header";
import About from "./Pages/About/About";
import Profile from "./Pages/Profile/Profile";
import Admin from "./Pages/Admin/Admin";
import ProtectedRoute from "./Components/Routes/ProtectedRoute";
import AdminRoute from "./Components/Routes/AdminRoute";
import Orders from "./Pages/Orders/Orders";
import Order from "./Pages/Orders/Order";
import Menus from "./Pages/Menus/Menus";
import Menu from "./Pages/Menus/Menu";
import Login from "./Pages/Authentication/Login";
import Register from "./Pages/Authentication/Register";
import FooterComponent from "./Components/Footer/Footer";



function App() {
  return (
    <BrowserRouter>
      <Header />

      <Routes>

        {/* Open Routes */}
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        {/* Open Routes */}



        {/* Authenticated Routes */}
        <Route element={<ProtectedRoute />}>
          <Route path="/profile" element={<Profile />} />
        </Route>

        <Route element={<ProtectedRoute />}>
          <Route path="/orders" element={<Orders />} />
        </Route>
        
        <Route element={<ProtectedRoute />}>
          <Route path="/order/:id" element={<Order />} />
        </Route>

        <Route element={<ProtectedRoute />}>
          <Route path="/menus" element={<Menus />} />
        </Route>


        <Route element={<ProtectedRoute />}>
          <Route path="/menu/:id" element={<Menu />} />
        </Route>

        {/* Authenticated Routes */}



        {/* Admin Route */}
        <Route element={<AdminRoute />}>
          <Route path="/admin" element={<Admin />} />
        </Route>
        {/* Admin Route */}



      </Routes>

      <FooterComponent />
    </BrowserRouter>
  );
}

export default App;
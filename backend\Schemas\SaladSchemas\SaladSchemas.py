from pydantic import BaseModel, Field
from typing import Optional, List

class Ingredient(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True

class SaladBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: float
    dressing: Optional[str] = Field(None, max_length=100)
    is_vegetarian: bool = False
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: bool = True

class SaladCreate(SaladBase):
    category_id: int
    menu_item_id: int
    ingredients: List[int] = []

class SaladUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: Optional[float] = None
    dressing: Optional[str] = Field(None, max_length=100)
    is_vegetarian: Optional[bool] = None
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: Optional[bool] = None
    category_id: Optional[int] = None
    menu_item_id: Optional[int] = None
    ingredients: List[int] = []

class Salad(SaladBase):
    id: int
    category_id: int
    menu_item_id: int
    ingredients: List[Ingredient] = []

    class Config:
        from_attributes = True


from sqlalchemy.orm import Session
from ...Models.Doners.DonerModel import Doner
from ...Models.Ingredients.IngredientsModel import Ingredient
from ...Models.Category.CategoryModel import Category

def seed_doners(db: Session):
    doners_to_seed = [
        {
            "name": "Classic Beef Doner",
            "description": "Tender beef slices wrapped in a warm flatbread with fresh vegetables and garlic sauce.",
            "price": 12.50,
            "meat_type": "Beef",
            "style": "Wrap",
            "image_url": "/images/doners/beef_doner.jpg",
            "is_available": True,
            "category_name": "Doner",
            "ingredients": ["Beef", "Flatbread", "Lettuce", "Tomato", "Onion", "Garlic Sauce"]
        },
        {
            "name": "Chicken Doner Plate",
            "description": "Juicy chicken doner served on a plate with rice, salad, and a side of tzatziki sauce.",
            "price": 15.00,
            "meat_type": "Chicken",
            "style": "Plate",
            "image_url": "/images/doners/chicken_doner_plate.jpg",
            "is_available": True,
            "category_name": "Doner",
            "ingredients": ["Chicken", "Rice", "Lettuce", "Cucumber", "Tomato", "Tzatziki Sauce"]
        },
        {
            "name": "Spicy Lamb Doner",
            "description": "A fiery lamb doner with jalapeños and spicy sauce for those who like it hot.",
            "price": 13.50,
            "meat_type": "Lamb",
            "style": "Wrap",
            "image_url": "/images/doners/spicy_lamb_doner.jpg",
            "is_available": True,
            "category_name": "Doner",
            "ingredients": ["Lamb", "Flatbread", "Onion", "Jalapeños", "Spicy Sauce", "Red Cabbage"]
        },
        {
            "name": "Veal Doner Sandwich",
            "description": "Thinly sliced veal in a pita bread pocket with fresh parsley and a tangy lemon dressing.",
            "price": 14.00,
            "meat_type": "Veal",
            "style": "Sandwich",
            "image_url": "/images/doners/veal_doner_sandwich.jpg",
            "is_available": True,
            "category_name": "Doner",
            "ingredients": ["Veal", "Pita Bread", "Parsley", "Tomato", "Onion", "Lemon"]
        },
        {
            "name": "Mixed Meat Doner",
            "description": "A hearty mix of beef and chicken doner, packed with flavor and fresh ingredients.",
            "price": 16.00,
            "meat_type": "Mixed",
            "style": "Wrap",
            "image_url": "/images/doners/mixed_meat_doner.jpg",
            "is_available": True,
            "category_name": "Doner",
            "ingredients": ["Beef", "Chicken", "Flatbread", "Lettuce", "Tomato", "Onion", "Garlic Sauce", "Spicy Sauce"]
        }
    ]

    for doner_data in doners_to_seed:
        doner_exists = db.query(Doner).filter(Doner.name == doner_data["name"]).first()
        if not doner_exists:
            category = db.query(Category).filter(Category.name == doner_data["category_name"]).first()
            if not category:
                continue

            new_doner = Doner(
                name=doner_data["name"],
                description=doner_data["description"],
                price=doner_data["price"],
                meat_type=doner_data["meat_type"],
                style=doner_data["style"],
                image_url=doner_data["image_url"],
                is_available=doner_data["is_available"],
                category_id=category.id
            )

            ingredients = db.query(Ingredient).filter(Ingredient.name.in_(doner_data["ingredients"])).all()
            new_doner.ingredients.extend(ingredients)

            db.add(new_doner)

    db.commit()

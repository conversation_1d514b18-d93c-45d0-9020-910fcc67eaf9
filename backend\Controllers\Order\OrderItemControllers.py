from fastapi import HTTPEx<PERSON>
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Order.OrderItem import OrderItem as OrderItemModel
from ...Schemas.OrderSchemas.OrderItemSchemas import OrderItem, OrderItemCreate, OrderItemUpdate


class OrderItemControllers:
    
    @staticmethod
    async def get_order_item(db: Session, order_item_id: int) -> OrderItemModel:
        """Get a single order item by ID"""
        order_item = db.query(OrderItemModel).filter(OrderItemModel.id == order_item_id).first()
        if not order_item:
            raise HTTPException(status_code=404, detail="Order item not found")
        return order_item
    
    @staticmethod
    async def get_order_items(db: Session, skip: int = 0, limit: int = 100) -> List[OrderItemModel]:
        """Get all order items with pagination"""
        return db.query(OrderItemModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_order_item(db: Session, order_item: OrderItemCreate) -> OrderItemModel:
        """Create a new order item"""
        db_order_item = OrderItemModel(**order_item.model_dump())
        db.add(db_order_item)
        db.commit()
        db.refresh(db_order_item)
        return db_order_item
    
    @staticmethod
    async def update_order_item(db: Session, order_item_id: int, order_item: OrderItemUpdate) -> OrderItemModel:
        """Update an existing order item"""
        db_order_item = db.query(OrderItemModel).filter(OrderItemModel.id == order_item_id).first()
        if not db_order_item:
            raise HTTPException(status_code=404, detail="Order item not found")
        
        update_data = order_item.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_order_item, key, value)
        
        db.commit()
        db.refresh(db_order_item)
        return db_order_item
    
    @staticmethod
    async def delete_order_item(db: Session, order_item_id: int) -> OrderItemModel:
        """Delete an order item"""
        db_order_item = db.query(OrderItemModel).filter(OrderItemModel.id == order_item_id).first()
        if not db_order_item:
            raise HTTPException(status_code=404, detail="Order item not found")
        
        db.delete(db_order_item)
        db.commit()
        return db_order_item

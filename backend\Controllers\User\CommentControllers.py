from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.User.CommentModel import Comment as CommentModel
from ...Schemas.UserSchemas.CommentSchemas import Comment, CommentCreate, CommentUpdate


class CommentControllers:
    
    @staticmethod
    async def get_comment(db: Session, comment_id: int) -> Optional[CommentModel]:
        """Get a single comment by ID"""
        comment = db.query(CommentModel).filter(CommentModel.id == comment_id).first()
        if not comment:
            raise HTTPException(status_code=404, detail="Comment not found")
        return comment
    
    @staticmethod
    async def get_comments(db: Session, skip: int = 0, limit: int = 100) -> List[CommentModel]:
        """Get all comments with pagination"""
        return db.query(CommentModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_comment(db: Session, comment: CommentCreate) -> CommentModel:
        """Create a new comment"""
        db_comment = CommentModel(**comment.model_dump())
        db.add(db_comment)
        db.commit()
        db.refresh(db_comment)
        return db_comment
    
    @staticmethod
    async def update_comment(db: Session, comment_id: int, comment: CommentUpdate) -> CommentModel:
        """Update an existing comment"""
        db_comment = db.query(CommentModel).filter(CommentModel.id == comment_id).first()
        if not db_comment:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        update_data = comment.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_comment, key, value)
        
        db.commit()
        db.refresh(db_comment)
        return db_comment
    
    @staticmethod
    async def delete_comment(db: Session, comment_id: int) -> CommentModel:
        """Delete a comment"""
        db_comment = db.query(CommentModel).filter(CommentModel.id == comment_id).first()
        if not db_comment:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        db.delete(db_comment)
        db.commit()
        return db_comment

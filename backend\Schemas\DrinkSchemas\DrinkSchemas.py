from pydantic import BaseModel, Field
from typing import Optional

class DrinkBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: float
    size_ml: int
    is_carbonated: bool = False
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: bool = True

class DrinkCreate(DrinkBase):
    category_id: int
    menu_item_id: int

class DrinkUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: Optional[float] = None
    size_ml: Optional[int] = None
    is_carbonated: Optional[bool] = None
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: Optional[bool] = None
    category_id: Optional[int] = None
    menu_item_id: Optional[int] = None

class Drink(DrinkBase):
    id: int
    category_id: int
    menu_item_id: int

    class Config:
        from_attributes = True

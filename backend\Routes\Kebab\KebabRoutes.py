from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Kebab.KebabControllers import KebabControllers
from ...DataBase.DataBase import get_db
from ...Schemas.KebabSchemas.KebabSchemas import <PERSON><PERSON><PERSON>, KebabCreate, KebabUpdate

Kebab_Router = APIRouter(
    prefix="/kebabs",
    tags=["kebabs"],
)

# Create Kebab
@Kebab_Router.post("/", response_model=Kebab)
async def create_kebab_route(kebab: KebabCreate, db: Session = Depends(get_db)):
    return await KebabControllers.create_kebab(db, kebab)

# Get All Kebabs
@Kebab_Router.get("/", response_model=List[Kebab])
async def read_kebabs_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await KebabControllers.get_kebabs(db, skip, limit)

# Get Single Kebab
@Kebab_Router.get("/{kebab_id}", response_model=Kebab)
async def read_kebab_route(kebab_id: int, db: Session = Depends(get_db)):
    return await KebabControllers.get_kebab(db, kebab_id)

# Update Kebab
@Kebab_Router.put("/{kebab_id}", response_model=Kebab)
async def update_kebab_route(kebab_id: int, kebab: KebabUpdate, db: Session = Depends(get_db)):
    return await KebabControllers.update_kebab(db, kebab_id, kebab)

# Delete Kebab
@Kebab_Router.delete("/{kebab_id}", response_model=Kebab)
async def delete_kebab_route(kebab_id: int, db: Session = Depends(get_db)):
    return await KebabControllers.delete_kebab(db, kebab_id)
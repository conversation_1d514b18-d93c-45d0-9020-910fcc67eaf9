from ...DataBase.DataBase import get_db
from ...Models.User.UserModel import User, UserRole
from ...Schemas.UserSchemas.UserSchemas import UserCreate, UserLogin, User, UserUpdate, Token
from ...Utils.HashPassword import Hash<PERSON>ass<PERSON>
from ...Utils.JWT import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List


hasher = HashPassword()
auth_handler = AuthHandler()

class UserControllers:

    @staticmethod
    async def register_user(user: UserCreate, db: Session = Depends(get_db)):
        existing_user = db.query(User).filter(User.email == user.email).first()
        if existing_user:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered")

        existing_username = db.query(User).filter(User.username == user.username).first()
        if existing_username:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Username already taken")

        hashed_password = hasher.create_hash(user.password)
        db_user = User(
            username=user.username,
            email=user.email,
            hashed_password=hashed_password,
            first_name=user.first_name,
            last_name=user.last_name,
            phone_number=user.phone_number,
            is_active=True, # New users are active by default
            role=UserRole.CUSTOMER # New users are customers by default
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user

    @staticmethod
    async def login_user(user_login: UserLogin, db: Session = Depends(get_db)):
        user = db.query(User).filter(User.username == user_login.username).first()
        if not user or not hasher.verify_hash(user_login.password, user.hashed_password):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid username or password")

        token = auth_handler.encode_token(user.id)
        return {"access_token": token, "token_type": "bearer"}

    @staticmethod
    async def get_all_users(current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user or current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this resource")
        users = db.query(User).all()
        return users

    @staticmethod
    async def get_single_user(user_id: int, current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user or (current_user.id != user_id and current_user.role != UserRole.ADMIN):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this user's data")

        return user

    @staticmethod
    async def update_user(user_id: int, user_update: UserUpdate, current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user or (current_user.id != user_id and current_user.role != UserRole.ADMIN):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to update this user's data")

        # Update fields from the user_update schema
        for field, value in user_update.model_dump(exclude_unset=True).items():
            if field == "password": # Handle password hashing if provided
                setattr(user, "hashed_password", hasher.create_hash(value))
            else:
                setattr(user, field, value)

        db.commit()
        db.refresh(user)
        return user

    @staticmethod
    async def delete_user(user_id: int, current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user or current_user.role != UserRole.ADMIN:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete users")

        db.delete(user)
        db.commit()
        return # 204 No Content
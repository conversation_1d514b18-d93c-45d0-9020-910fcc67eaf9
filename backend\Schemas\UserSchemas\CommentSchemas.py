from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class CommentBase(BaseModel):
    content: str

class CommentCreate(CommentBase):
    user_id: int
    menu_item_id: int

class CommentUpdate(BaseModel):
    content: Optional[str] = None

class Comment(CommentBase):
    id: int
    user_id: int
    menu_item_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Kebabs.KebabModel import Kebab as KebabModel
from ...Schemas.KebabSchemas.KebabSchemas import <PERSON><PERSON><PERSON>, KebabCreate, KebabUpdate


class KebabControllers:
    
    @staticmethod
    async def get_kebab(db: Session, kebab_id: int) -> KebabModel:
        """Get a single kebab by ID"""
        kebab = db.query(KebabModel).filter(KebabModel.id == kebab_id).first()
        if not kebab:
            raise HTTPException(status_code=404, detail="Kebab not found")
        return kebab
    
    @staticmethod
    async def get_kebabs(db: Session, skip: int = 0, limit: int = 100) -> List[KebabModel]:
        """Get all kebabs with pagination"""
        return db.query(KebabModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_kebab(db: Session, kebab: KebabCreate) -> KebabModel:
        """Create a new kebab"""
        db_kebab = KebabModel(**kebab.model_dump())
        db.add(db_kebab)
        db.commit()
        db.refresh(db_kebab)
        return db_kebab
    
    @staticmethod
    async def update_kebab(db: Session, kebab_id: int, kebab: KebabUpdate) -> KebabModel:
        """Update an existing kebab"""
        db_kebab = db.query(KebabModel).filter(KebabModel.id == kebab_id).first()
        if not db_kebab:
            raise HTTPException(status_code=404, detail="Kebab not found")
        
        update_data = kebab.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_kebab, key, value)
        
        db.commit()
        db.refresh(db_kebab)
        return db_kebab
    
    @staticmethod
    async def delete_kebab(db: Session, kebab_id: int) -> KebabModel:
        """Delete a kebab"""
        db_kebab = db.query(KebabModel).filter(KebabModel.id == kebab_id).first()
        if not db_kebab:
            raise HTTPException(status_code=404, detail="Kebab not found")
        
        db.delete(db_kebab)
        db.commit()
        return db_kebab

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// Types for your store state
export interface User {
  id: string;
  name: string;
  email: string;
  role?: string;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  available: boolean;
}

export interface CartItem extends MenuItem {
  quantity: number;
}

export interface Order {
  id: string;
  items: CartItem[];
  total: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered';
  createdAt: string;
}

export interface Reservation {
  id: string;
  customerName: string;
  email: string;
  phone: string;
  date: string;
  time: string;
  guests: number;
  status: 'pending' | 'confirmed' | 'cancelled';
}

// Main store interface
interface AppState {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  
  // Menu state
  menuItems: MenuItem[];
  categories: string[];
  
  // Cart state
  cart: CartItem[];
  cartTotal: number;
  
  // Orders state
  orders: Order[];
  currentOrder: Order | null;
  
  // Reservations state
  reservations: Reservation[];
  
  // UI state
  isLoading: boolean;
  error: string | null;
  
  // Actions
  // User actions
  setUser: (user: User | null) => void;
  login: (user: User) => void;
  logout: () => void;
  
  // Menu actions
  setMenuItems: (items: MenuItem[]) => void;
  setCategories: (categories: string[]) => void;
  
  // Cart actions
  addToCart: (item: MenuItem, quantity?: number) => void;
  removeFromCart: (itemId: string) => void;
  updateCartItemQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  calculateCartTotal: () => void;
  
  // Order actions
  setOrders: (orders: Order[]) => void;
  addOrder: (order: Order) => void;
  setCurrentOrder: (order: Order | null) => void;
  
  // Reservation actions
  setReservations: (reservations: Reservation[]) => void;
  addReservation: (reservation: Reservation) => void;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// Create the store
export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        isAuthenticated: false,
        menuItems: [],
        categories: [],
        cart: [],
        cartTotal: 0,
        orders: [],
        currentOrder: null,
        reservations: [],
        isLoading: false,
        error: null,

        // User actions
        setUser: (user) => set({ user, isAuthenticated: !!user }),
        
        login: (user) => set({ 
          user, 
          isAuthenticated: true,
          error: null 
        }),
        
        logout: () => set({ 
          user: null, 
          isAuthenticated: false,
          cart: [],
          cartTotal: 0,
          currentOrder: null 
        }),

        // Menu actions
        setMenuItems: (items) => set({ menuItems: items }),
        
        setCategories: (categories) => set({ categories }),

        // Cart actions
        addToCart: (item, quantity = 1) => {
          const { cart } = get();
          const existingItem = cart.find(cartItem => cartItem.id === item.id);
          
          if (existingItem) {
            // Update quantity if item already exists
            const updatedCart = cart.map(cartItem =>
              cartItem.id === item.id
                ? { ...cartItem, quantity: cartItem.quantity + quantity }
                : cartItem
            );
            set({ cart: updatedCart });
          } else {
            // Add new item to cart
            const newCartItem: CartItem = { ...item, quantity };
            set({ cart: [...cart, newCartItem] });
          }
          
          // Recalculate total
          get().calculateCartTotal();
        },

        removeFromCart: (itemId) => {
          const { cart } = get();
          const updatedCart = cart.filter(item => item.id !== itemId);
          set({ cart: updatedCart });
          get().calculateCartTotal();
        },

        updateCartItemQuantity: (itemId, quantity) => {
          const { cart } = get();
          if (quantity <= 0) {
            get().removeFromCart(itemId);
            return;
          }
          
          const updatedCart = cart.map(item =>
            item.id === itemId ? { ...item, quantity } : item
          );
          set({ cart: updatedCart });
          get().calculateCartTotal();
        },

        clearCart: () => set({ cart: [], cartTotal: 0 }),

        calculateCartTotal: () => {
          const { cart } = get();
          const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
          set({ cartTotal: total });
        },

        // Order actions
        setOrders: (orders) => set({ orders }),
        
        addOrder: (order) => {
          const { orders } = get();
          set({ orders: [...orders, order] });
        },
        
        setCurrentOrder: (order) => set({ currentOrder: order }),

        // Reservation actions
        setReservations: (reservations) => set({ reservations }),
        
        addReservation: (reservation) => {
          const { reservations } = get();
          set({ reservations: [...reservations, reservation] });
        },

        // UI actions
        setLoading: (loading) => set({ isLoading: loading }),
        
        setError: (error) => set({ error }),
      }),
      {
        name: 'restaurant-app-storage', // localStorage key
        partialize: (state) => ({
          // Only persist certain parts of the state
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          cart: state.cart,
          cartTotal: state.cartTotal,
        }),
      }
    ),
    {
      name: 'restaurant-app-store', // DevTools name
    }
  )
);

// Selector hooks for better performance (optional but recommended)
export const useUser = () => useAppStore((state) => state.user);
export const useAuth = () => useAppStore((state) => ({
  user: state.user,
  isAuthenticated: state.isAuthenticated,
  login: state.login,
  logout: state.logout,
}));

export const useCart = () => useAppStore((state) => ({
  cart: state.cart,
  cartTotal: state.cartTotal,
  addToCart: state.addToCart,
  removeFromCart: state.removeFromCart,
  updateCartItemQuantity: state.updateCartItemQuantity,
  clearCart: state.clearCart,
}));

export const useMenu = () => useAppStore((state) => ({
  menuItems: state.menuItems,
  categories: state.categories,
  setMenuItems: state.setMenuItems,
  setCategories: state.setCategories,
}));

export const useOrders = () => useAppStore((state) => ({
  orders: state.orders,
  currentOrder: state.currentOrder,
  setOrders: state.setOrders,
  addOrder: state.addOrder,
  setCurrentOrder: state.setCurrentOrder,
}));

export const useUI = () => useAppStore((state) => ({
  isLoading: state.isLoading,
  error: state.error,
  setLoading: state.setLoading,
  setError: state.setError,
}));

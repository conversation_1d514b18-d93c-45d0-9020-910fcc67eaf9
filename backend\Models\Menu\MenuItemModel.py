from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Float, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class MenuItem(Base):
    __tablename__ = 'menu_items'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)
    image_url = Column(String(255))
    is_available = Column(Boolean, default=True)

    # Relationships to specific product types (one-to-one)
    drink = relationship("Drink", back_populates="menu_item", uselist=False)
    kebab = relationship("Kebab", back_populates="menu_item", uselist=False)
    doner = relationship("Doner", back_populates="menu_item", uselist=False)
    salad = relationship("Salad", back_populates="menu_item", uselist=False)

    # Relationships for comments and reviews
    comments = relationship("Comment", back_populates="menu_item")
    reviews = relationship("Review", back_populates="menu_item")

    # Relationship to order items
    order_items = relationship("OrderItem", back_populates="menu_item")

    def __repr__(self):
        return f"<MenuItem(id={self.id}, name='{self.name}')>"

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Order.OrderItemControllers import OrderItemControllers
from ...DataBase.DataBase import get_db
from ...Schemas.OrderSchemas.OrderItemSchemas import OrderItem, OrderItemCreate, OrderItemUpdate

OrderItem_Router = APIRouter(
    prefix="/order_items",
    tags=["order_items"],
)

# Create Order Item
@OrderItem_Router.post("/", response_model=OrderItem)
async def create_order_item_route(order_item: OrderItemCreate, db: Session = Depends(get_db)):
    return await OrderItemControllers.create_order_item(db, order_item)

# Get All Order Items
@OrderItem_Router.get("/", response_model=List[OrderItem])
async def read_order_items_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await OrderItemControllers.get_order_items(db, skip, limit)

# Get Single Order Item
@OrderItem_Router.get("/{order_item_id}", response_model=OrderItem)
async def read_order_item_route(order_item_id: int, db: Session = Depends(get_db)):
    return await OrderItemControllers.get_order_item(db, order_item_id)

# Update Order Item
@OrderItem_Router.put("/{order_item_id}", response_model=OrderItem)
async def update_order_item_route(order_item_id: int, order_item: OrderItemUpdate, db: Session = Depends(get_db)):
    return await OrderItemControllers.update_order_item(db, order_item_id, order_item)

# Delete Order Item
@OrderItem_Router.delete("/{order_item_id}", response_model=OrderItem)
async def delete_order_item_route(order_item_id: int, db: Session = Depends(get_db)):
    return await OrderItemControllers.delete_order_item(db, order_item_id)
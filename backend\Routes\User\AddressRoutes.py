from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.User.AddressControllers import AddressControllers
from ...DataBase.DataBase import get_db
from ...Schemas.UserSchemas.AddressSchemas import Address, AddressCreate, AddressUpdate

Address_Router = APIRouter(
    prefix="/addresses",
    tags=["addresses"],
)

# Create Address
@Address_Router.post("/", response_model=Address)
async def create_address_route(address: AddressCreate, db: Session = Depends(get_db)):
    return await AddressControllers.create_address(db, address)

# Get All Addresses
@Address_Router.get("/", response_model=List[Address])
async def read_addresses_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await AddressControllers.get_addresses(db, skip, limit)

# Get Single Address
@Address_Router.get("/{address_id}", response_model=Address)
async def read_address_route(address_id: int, db: Session = Depends(get_db)):
    return await AddressControllers.get_address(db, address_id)

# Update Address
@Address_Router.put("/{address_id}", response_model=Address)
async def update_address_route(address_id: int, address: AddressUpdate, db: Session = Depends(get_db)):
    return await AddressControllers.update_address(db, address_id, address)

# Delete Address
@Address_Router.delete("/{address_id}", response_model=Address)
async def delete_address_route(address_id: int, db: Session = Depends(get_db)):
    return await AddressControllers.delete_address(db, address_id)

from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Menu.MenuModel import Menu as MenuModel
from ...Schemas.MenuSchemas.MenuSchemas import <PERSON><PERSON>, MenuCreate, MenuUpdate


class MenuControllers:
    
    @staticmethod
    async def get_menu(db: Session, menu_id: int) -> MenuModel:
        """Get a single menu by ID"""
        menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
        if not menu:
            raise HTTPException(status_code=404, detail="Menu not found")
        return menu
    
    @staticmethod
    async def get_menus(db: Session, skip: int = 0, limit: int = 100) -> List[MenuModel]:
        """Get all menus with pagination"""
        return db.query(MenuModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_menu(db: Session, menu: MenuCreate) -> MenuModel:
        """Create a new menu"""
        menu_data = menu.model_dump()

        # Validate that exactly one main course is provided
        has_kebab = menu_data.get('kebab_id') is not None
        has_doner = menu_data.get('doner_id') is not None

        if not (has_kebab ^ has_doner):  # XOR: exactly one should be True
            raise HTTPException(
                status_code=400,
                detail="Menu must have exactly one main course: either kebab_id or doner_id, but not both"
            )

        # Note: Foreign key constraints will handle validation of referenced items

        db_menu = MenuModel(**menu_data)
        db.add(db_menu)
        db.commit()
        db.refresh(db_menu)
        return db_menu
    
    @staticmethod
    async def update_menu(db: Session, menu_id: int, menu: MenuUpdate) -> MenuModel:
        """Update an existing menu"""
        db_menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
        if not db_menu:
            raise HTTPException(status_code=404, detail="Menu not found")
        
        update_data = menu.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_menu, key, value)
        
        db.commit()
        db.refresh(db_menu)
        return db_menu
    
    @staticmethod
    async def delete_menu(db: Session, menu_id: int) -> MenuModel:
        """Delete a menu"""
        db_menu = db.query(MenuModel).filter(MenuModel.id == menu_id).first()
        if not db_menu:
            raise HTTPException(status_code=404, detail="Menu not found")
        
        db.delete(db_menu)
        db.commit()
        return db_menu

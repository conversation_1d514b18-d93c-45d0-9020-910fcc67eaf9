from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Doner.DonerControllers import DonerControllers
from ...DataBase.DataBase import get_db
from ...Schemas.DonerSchemas.DonerSchemas import Doner, Doner<PERSON><PERSON>, DonerUpdate

Doner_Router = APIRouter(
    prefix="/doners",
    tags=["doners"],
)

# Create Doner
@Doner_Router.post("/", response_model=Doner)
async def create_doner_route(doner: DonerCreate, db: Session = Depends(get_db)):
    return await DonerControllers.create_doner(db, doner)

# Get All Doners
@Doner_Router.get("/", response_model=List[Doner])
async def read_doners_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await DonerControllers.get_doners(db, skip, limit)

# Get Single Doner
@Doner_Router.get("/{doner_id}", response_model=Doner)
async def read_doner_route(doner_id: int, db: Session = Depends(get_db)):
    return await DonerControllers.get_doner(db, doner_id)

# Update Doner
@Doner_Router.put("/{doner_id}", response_model=Doner)
async def update_doner_route(doner_id: int, doner: DonerUpdate, db: Session = Depends(get_db)):
    return await DonerControllers.update_doner(db, doner_id, doner)

# Delete Doner
@Doner_Router.delete("/{doner_id}", response_model=Doner)
async def delete_doner_route(doner_id: int, db: Session = Depends(get_db)):
    return await DonerControllers.delete_doner(db, doner_id)
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Menu.MenuControllers import MenuControllers
from ...DataBase.DataBase import get_db
from ...Schemas.MenuSchemas.MenuSchemas import Menu, MenuCreate, MenuUpdate

Menu_Router = APIRouter(
    prefix="/menus",
    tags=["menus"],
)

# Create Menu
@Menu_Router.post("/", response_model=Menu)
async def create_menu_route(menu: MenuCreate, db: Session = Depends(get_db)):
    return await MenuControllers.create_menu(db, menu)

# Get All Menus
@Menu_Router.get("/", response_model=List[Menu])
async def read_menus_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await MenuControllers.get_menus(db, skip, limit)

# Get Single Menu
@Menu_Router.get("/{menu_id}", response_model=Menu)
async def read_menu_route(menu_id: int, db: Session = Depends(get_db)):
    return await MenuControllers.get_menu(db, menu_id)

# Update Menu
@Menu_Router.put("/{menu_id}", response_model=Menu)
async def update_menu_route(menu_id: int, menu: MenuUpdate, db: Session = Depends(get_db)):
    return await MenuControllers.update_menu(db, menu_id, menu)

# Delete Menu
@Menu_Router.delete("/{menu_id}", response_model=Menu)
async def delete_menu_route(menu_id: int, db: Session = Depends(get_db)):
    return await MenuControllers.delete_menu(db, menu_id)
from pydantic import BaseModel, Field
from typing import Optional, List
from ..MenuSchemas.MenuItemSchemas import MenuItem

class MenuBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: float

class MenuCreate(MenuBase):
    drink_id: int
    salad_id: int
    main_course_id: int

class MenuUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: Optional[float] = None
    drink_id: Optional[int] = None
    salad_id: Optional[int] = None
    main_course_id: Optional[int] = None

class Menu(MenuBase):
    id: int
    drink: MenuItem
    salad: MenuItem
    main_course: MenuItem

    class Config:
        from_attributes = True

from pydantic import BaseModel, Field
from typing import Optional, List

class Ingredient(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True

class DonerBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: float
    meat_type: Optional[str] = Field(None, max_length=50)
    style: Optional[str] = Field(None, max_length=50)
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: bool = True

class DonerCreate(DonerBase):
    category_id: int
    menu_item_id: int
    ingredients: List[int] = []

class DonerUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: Optional[float] = None
    meat_type: Optional[str] = Field(None, max_length=50)
    style: Optional[str] = Field(None, max_length=50)
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: Optional[bool] = None
    category_id: Optional[int] = None
    menu_item_id: Optional[int] = None
    ingredients: List[int] = []

class Doner(DonerBase):
    id: int
    category_id: int
    menu_item_id: int
    ingredients: List[Ingredient] = []

    class Config:
        from_attributes = True

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Ingredient.IngredientControllers import IngredientControllers
from ...DataBase.DataBase import get_db
from ...Schemas.IngredientSchemas.IngredientSchemas import Ingredient, IngredientCreate, IngredientUpdate

Ingredient_Router = APIRouter(
    prefix="/ingredients",
    tags=["ingredients"],
)

# Create Ingredient
@Ingredient_Router.post("/", response_model=Ingredient)
async def create_ingredient_route(ingredient: IngredientCreate, db: Session = Depends(get_db)):
    return await IngredientControllers.create_ingredient(db, ingredient)

# Get All Ingredients
@Ingredient_Router.get("/", response_model=List[Ingredient])
async def read_ingredients_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await IngredientControllers.get_ingredients(db, skip, limit)

# Get Single Ingredient
@Ingredient_Router.get("/{ingredient_id}", response_model=Ingredient)
async def read_ingredient_route(ingredient_id: int, db: Session = Depends(get_db)):
    return await IngredientControllers.get_ingredient(db, ingredient_id)

# Update Ingredient
@Ingredient_Router.put("/{ingredient_id}", response_model=Ingredient)
async def update_ingredient_route(ingredient_id: int, ingredient: IngredientUpdate, db: Session = Depends(get_db)):
    return await IngredientControllers.update_ingredient(db, ingredient_id, ingredient)

# Delete Ingredient
@Ingredient_Router.delete("/{ingredient_id}", response_model=Ingredient)
async def delete_ingredient_route(ingredient_id: int, db: Session = Depends(get_db)):
    return await IngredientControllers.delete_ingredient(db, ingredient_id)
from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Message.MessageModel import Message as MessageModel
from ...Schemas.MessageSchemas.MessageSchemas import Message, MessageCreate, MessageUpdate


class MessageControllers:
    
    @staticmethod
    async def get_message(db: Session, message_id: int) -> MessageModel:
        """Get a single message by ID"""
        message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
        if not message:
            raise HTTPException(status_code=404, detail="Message not found")
        return message
    
    @staticmethod
    async def get_messages(db: Session, skip: int = 0, limit: int = 100) -> List[MessageModel]:
        """Get all messages with pagination"""
        return db.query(MessageModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_message(db: Session, message: MessageCreate) -> MessageModel:
        """Create a new message"""
        db_message = MessageModel(**message.model_dump())
        db.add(db_message)
        db.commit()
        db.refresh(db_message)
        return db_message
    
    @staticmethod
    async def update_message(db: Session, message_id: int, message: MessageUpdate) -> MessageModel:
        """Update an existing message"""
        db_message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
        if not db_message:
            raise HTTPException(status_code=404, detail="Message not found")
        
        update_data = message.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_message, key, value)
        
        db.commit()
        db.refresh(db_message)
        return db_message
    
    @staticmethod
    async def delete_message(db: Session, message_id: int) -> MessageModel:
        """Delete a message"""
        db_message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
        if not db_message:
            raise HTTPException(status_code=404, detail="Message not found")
        
        db.delete(db_message)
        db.commit()
        return db_message

from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Order.OrderModel import Order as OrderModel
from ...Schemas.OrderSchemas.OrderSchemas import Order, OrderCreate, OrderUpdate


class OrderControllers:
    
    @staticmethod
    async def get_order(db: Session, order_id: int) -> OrderModel:
        """Get a single order by ID"""
        order = db.query(OrderModel).filter(OrderModel.id == order_id).first()
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")
        return order
    
    @staticmethod
    async def get_orders(db: Session, skip: int = 0, limit: int = 100) -> List[OrderModel]:
        """Get all orders with pagination"""
        return db.query(OrderModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_order(db: Session, order: OrderCreate) -> OrderModel:
        """Create a new order"""
        db_order = OrderModel(**order.model_dump())
        db.add(db_order)
        db.commit()
        db.refresh(db_order)
        return db_order
    
    @staticmethod
    async def update_order(db: Session, order_id: int, order: OrderUpdate) -> OrderModel:
        """Update an existing order"""
        db_order = db.query(OrderModel).filter(OrderModel.id == order_id).first()
        if not db_order:
            raise HTTPException(status_code=404, detail="Order not found")
        
        update_data = order.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_order, key, value)
        
        db.commit()
        db.refresh(db_order)
        return db_order
    
    @staticmethod
    async def delete_order(db: Session, order_id: int) -> OrderModel:
        """Delete an order"""
        db_order = db.query(OrderModel).filter(OrderModel.id == order_id).first()
        if not db_order:
            raise HTTPException(status_code=404, detail="Order not found")
        
        db.delete(db_order)
        db.commit()
        return db_order

from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
from ...Models.User.UserModel import UserRole

# Base schema for common user attributes
class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    first_name: str = Field(..., min_length=1, max_length=50)
    last_name: str = Field(..., min_length=1, max_length=50)
    phone_number: Optional[str] = None

# Schema for creating a new user (registration)
class UserCreate(UserBase):
    password: str = Field(..., min_length=6)

# Schema for updating a user
class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    phone_number: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[UserRole] = None

# Schema for representing a user in the database (used for responses)
class User(UserBase):
    id: int
    is_active: bool
    role: UserRole
    created_at: datetime

    class Config:
        from_attributes = True

# Schema for user login
class UserLogin(BaseModel):
    username: str
    password: str

# Schema for the JWT token
class Token(BaseModel):
    access_token: str
    token_type: str

# Schema for the data embedded in the token
class TokenData(BaseModel):
    username: Optional[str] = None

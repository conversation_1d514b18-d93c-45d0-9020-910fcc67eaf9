from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Drinks.DrinksModel import Drink as DrinkModel
from ...Schemas.DrinkSchemas.DrinkSchemas import Drink, Drink<PERSON>reate, DrinkUpdate


class DrinkControllers:
    
    @staticmethod
    async def get_drink(db: Session, drink_id: int) -> DrinkModel:
        """Get a single drink by ID"""
        drink = db.query(DrinkModel).filter(DrinkModel.id == drink_id).first()
        if not drink:
            raise HTTPException(status_code=404, detail="Drink not found")
        return drink
    
    @staticmethod
    async def get_drinks(db: Session, skip: int = 0, limit: int = 100) -> List[DrinkModel]:
        """Get all drinks with pagination"""
        return db.query(DrinkModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_drink(db: Session, drink: DrinkCreate) -> DrinkModel:
        """Create a new drink"""
        db_drink = DrinkModel(**drink.model_dump())
        db.add(db_drink)
        db.commit()
        db.refresh(db_drink)
        return db_drink
    
    @staticmethod
    async def update_drink(db: Session, drink_id: int, drink: DrinkUpdate) -> DrinkModel:
        """Update an existing drink"""
        db_drink = db.query(DrinkModel).filter(DrinkModel.id == drink_id).first()
        if not db_drink:
            raise HTTPException(status_code=404, detail="Drink not found")
        
        update_data = drink.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_drink, key, value)
        
        db.commit()
        db.refresh(db_drink)
        return db_drink
    
    @staticmethod
    async def delete_drink(db: Session, drink_id: int) -> DrinkModel:
        """Delete a drink"""
        db_drink = db.query(DrinkModel).filter(DrinkModel.id == drink_id).first()
        if not db_drink:
            raise HTTPException(status_code=404, detail="Drink not found")
        
        db.delete(db_drink)
        db.commit()
        return db_drink

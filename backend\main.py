from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware

from .DataBase.DataBase import get_db, engine, Base
from .Routes.User.UserRoutes import User_Router
from .Routes.User.AddressRoutes import router as Address_Router
from .Routes.User.CommentRoutes import router as Comment_Router
from .Routes.User.ReviewRoutes import router as Review_Router
from .Routes.Category.CategoryRoutes import router as Category_Router
from .Routes.Doner.DonerRoutes import router as Doner_Router
from .Routes.Drink.DrinkRoutes import router as Drink_Router
from .Routes.Ingredient.IngredientRoutes import router as Ingredient_Router
from .Routes.Kebab.KebabRoutes import router as Kebab_Router
from .Routes.Menu.MenuRoutes import router as Menu_Router
from .Routes.Menu.MenuItemRoutes import router as MenuItem_Router
from .Routes.Message.MessageRoutes import router as Message_Router
from .Routes.Order.OrderRoutes import router as Order_Router
from .Routes.Order.OrderItemRoutes import router as OrderItem_Router
from .Routes.Salad.SaladRoutes import router as Salad_Router

# Application metadata for Swagger UI
app = FastAPI(
    title="E-Commerce API",
    description="The main API for the E-Commerce application, handling products, users, and orders.",
    version="1.0.0",
    contact={
        "name": "API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "Apache 2.0",
        "url": "https://www.apache.org/licenses/LICENSE-20.0.html",
    },
)

# CORS (Cross-Origin Resource Sharing) Middleware
origins = [
    "http://localhost",
    "http://localhost:3000",  # Common port for React frontend
    "http://localhost:5173",  # Common port for Vite frontend
    # Add your deployed frontend URL here when you go to production
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Startup event to create database tables
@app.on_event("startup")
def on_startup():
    Base.metadata.create_all(bind=engine)
    print("Database tables created (if they didn't exist).")

# Include API routers
app.include_router(User_Router, prefix="/users", tags=["Users"])
app.include_router(Address_Router, prefix="/users", tags=["Users"])
app.include_router(Comment_Router, prefix="/users", tags=["Users"])
app.include_router(Review_Router, prefix="/users", tags=["Users"])
app.include_router(Category_Router, prefix="/categories", tags=["Categories"])
app.include_router(Doner_Router, prefix="/doners", tags=["Doners"])
app.include_router(Drink_Router, prefix="/drinks", tags=["Drinks"])
app.include_router(Ingredient_Router, prefix="/ingredients", tags=["Ingredients"])
app.include_router(Kebab_Router, prefix="/kebabs", tags=["Kebabs"])
app.include_router(Menu_Router, prefix="/menus", tags=["Menus"])
app.include_router(MenuItem_Router, prefix="/menu_items", tags=["Menu Items"])
app.include_router(Message_Router, prefix="/messages", tags=["Messages"])
app.include_router(Order_Router, prefix="/orders", tags=["Orders"])
app.include_router(OrderItem_Router, prefix="/order_items", tags=["Order Items"])
app.include_router(Salad_Router, prefix="/salads", tags=["Salads"])

# A simple root endpoint
@app.get("/", tags=["Root"])
async def read_root():
    """A simple GET endpoint to confirm the API is running."""
    return {"message": "Welcome to the E-Commerce API!"}
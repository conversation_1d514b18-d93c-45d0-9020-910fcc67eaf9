from pydantic import BaseModel, Field
from typing import List, Optional
from ..DonerSchemas.DonerSchemas import Done<PERSON>
from ..DrinkSchemas.DrinkSchemas import Drink
from ..KebabSchemas.KebabSchemas import Kebab
from ..SaladSchemas.SaladSchemas import Salad

class CategoryBase(BaseModel):
    name: str = Field(..., max_length=100)

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)

class Category(CategoryBase):
    id: int
    doners: List[Doner] = []
    drinks: List[Drink] = []
    kebabs: List[Kebab] = []
    salads: List[Salad] = []

    class Config:
        from_attributes = True

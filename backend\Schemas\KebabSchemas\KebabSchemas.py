from pydantic import BaseModel, Field
from typing import Optional, List

class Ingredient(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True

class KebabBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: float
    meat_type: Optional[str] = Field(None, max_length=50)
    spice_level: int = Field(1, ge=1, le=5)
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: bool = True

class KebabCreate(KebabBase):
    category_id: int
    menu_item_id: int
    ingredients: List[int] = []

class KebabUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: Optional[float] = None
    meat_type: Optional[str] = Field(None, max_length=50)
    spice_level: Optional[int] = Field(None, ge=1, le=5)
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: Optional[bool] = None
    category_id: Optional[int] = None
    menu_item_id: Optional[int] = None
    ingredients: List[int] = []

class Kebab(KebabBase):
    id: int
    category_id: int
    menu_item_id: int
    ingredients: List[Ingredient] = []

    class Config:
        from_attributes = True

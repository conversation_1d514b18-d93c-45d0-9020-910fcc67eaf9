from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Salads.SaladModel import Salad as SaladModel
from ...Schemas.SaladSchemas.SaladSchemas import Salad, SaladCreate, SaladUpdate


class SaladControllers:
    
    @staticmethod
    async def get_salad(db: Session, salad_id: int) -> SaladModel:
        """Get a single salad by ID"""
        salad = db.query(SaladModel).filter(SaladModel.id == salad_id).first()
        if not salad:
            raise HTTPException(status_code=404, detail="Salad not found")
        return salad
    
    @staticmethod
    async def get_salads(db: Session, skip: int = 0, limit: int = 100) -> List[SaladModel]:
        """Get all salads with pagination"""
        return db.query(SaladModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_salad(db: Session, salad: SaladCreate) -> SaladModel:
        """Create a new salad"""
        db_salad = SaladModel(**salad.model_dump())
        db.add(db_salad)
        db.commit()
        db.refresh(db_salad)
        return db_salad
    
    @staticmethod
    async def update_salad(db: Session, salad_id: int, salad: SaladUpdate) -> SaladModel:
        """Update an existing salad"""
        db_salad = db.query(SaladModel).filter(SaladModel.id == salad_id).first()
        if not db_salad:
            raise HTTPException(status_code=404, detail="Salad not found")
        
        update_data = salad.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_salad, key, value)
        
        db.commit()
        db.refresh(db_salad)
        return db_salad
    
    @staticmethod
    async def delete_salad(db: Session, salad_id: int) -> SaladModel:
        """Delete a salad"""
        db_salad = db.query(SaladModel).filter(SaladModel.id == salad_id).first()
        if not db_salad:
            raise HTTPException(status_code=404, detail="Salad not found")
        
        db.delete(db_salad)
        db.commit()
        return db_salad

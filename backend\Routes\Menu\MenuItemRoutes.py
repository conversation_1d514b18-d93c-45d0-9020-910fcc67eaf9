from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Menu.MenuItemControllers import MenuItemControllers
from ...DataBase.DataBase import get_db
from ...Schemas.MenuSchemas.MenuItemSchemas import MenuItem, MenuItemCreate, MenuItemUpdate

MenuItem_Router = APIRouter(
    prefix="/menu_items",
    tags=["menu_items"],
)

# Create Menu Item
@MenuItem_Router.post("/", response_model=MenuItem)
async def create_menu_item_route(menu_item: MenuItemCreate, db: Session = Depends(get_db)):
    return await MenuItemControllers.create_menu_item(db, menu_item)

# Get All Menu Items
@MenuItem_Router.get("/", response_model=List[MenuItem])
async def read_menu_items_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await MenuItemControllers.get_menu_items(db, skip, limit)

# Get Single Menu Item
@MenuItem_Router.get("/{menu_item_id}", response_model=MenuItem)
async def read_menu_item_route(menu_item_id: int, db: Session = Depends(get_db)):
    return await MenuItemControllers.get_menu_item(db, menu_item_id)

# Update Menu Item
@MenuItem_Router.put("/{menu_item_id}", response_model=MenuItem)
async def update_menu_item_route(menu_item_id: int, menu_item: MenuItemUpdate, db: Session = Depends(get_db)):
    return await MenuItemControllers.update_menu_item(db, menu_item_id, menu_item)

# Delete Menu Item
@MenuItem_Router.delete("/{menu_item_id}", response_model=MenuItem)
async def delete_menu_item_route(menu_item_id: int, db: Session = Depends(get_db)):
    return await MenuItemControllers.delete_menu_item(db, menu_item_id)
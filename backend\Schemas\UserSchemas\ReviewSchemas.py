from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class ReviewBase(BaseModel):
    rating: int = Field(..., ge=1, le=5)

class ReviewCreate(ReviewBase):
    user_id: int
    menu_item_id: int

class ReviewUpdate(BaseModel):
    rating: Optional[int] = Field(None, ge=1, le=5)

class Review(ReviewBase):
    id: int
    user_id: int
    menu_item_id: int
    created_at: datetime

    class Config:
        from_attributes = True

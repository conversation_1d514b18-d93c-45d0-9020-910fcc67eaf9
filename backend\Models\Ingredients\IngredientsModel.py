from sqlalchemy import Column, Integer, String, Table, ForeignKey
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

# Association Tables defined in the respective food models

class Ingredient(Base):
    __tablename__ = 'ingredients'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)

    # Relationships (many-to-many)
    doners = relationship("Doner", secondary="doner_ingredients", back_populates="ingredients")
    kebabs = relationship("Kebab", secondary="kebab_ingredients", back_populates="ingredients")
    salads = relationship("Salad", secondary="salad_ingredients", back_populates="ingredients")

    def __repr__(self):
        return f"<Ingredient(id={self.id}, name='{self.name}')>"

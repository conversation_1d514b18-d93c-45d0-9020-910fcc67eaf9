["-4px", "-bottom-1", "-left-1", "-right-1", "-space-x-4", "-top-1", "absolute", "bg-blue-700", "bg-cyan-700", "bg-gray-100", "bg-gray-400", "bg-gray-700", "bg-gray-800", "bg-gray-900", "bg-green-400", "bg-green-700", "bg-indigo-700", "bg-lime-700", "bg-pink-700", "bg-primary-700", "bg-purple-700", "bg-red-400", "bg-red-700", "bg-teal-700", "bg-white", "bg-yellow-400", "block", "border", "border-2", "border-b", "border-blue-700", "border-cyan-700", "border-gray-100", "border-gray-200", "border-gray-300", "border-gray-700", "border-gray-800", "border-green-700", "border-indigo-700", "border-l-0", "border-lime-700", "border-pink-700", "border-primary-700", "border-purple-700", "border-red-700", "border-teal-700", "border-white", "border-yellow-400", "bottom-center", "bottom-left", "bottom-right", "center-left", "center-right", "container", "cursor-pointer", "dark:bg-blue-600", "dark:bg-cyan-600", "dark:bg-gray-600", "dark:bg-gray-700", "dark:bg-gray-800", "dark:bg-green-600", "dark:bg-indigo-600", "dark:bg-lime-600", "dark:bg-pink-600", "dark:bg-primary-600", "dark:bg-purple-600", "dark:bg-red-600", "dark:bg-teal-600", "dark:bg-yellow-600", "dark:border-blue-500", "dark:border-cyan-500", "dark:border-gray-600", "dark:border-gray-700", "dark:border-gray-800", "dark:border-green-600", "dark:border-indigo-600", "dark:border-lime-600", "dark:border-none", "dark:border-pink-600", "dark:border-primary-600", "dark:border-purple-600", "dark:border-red-600", "dark:border-teal-600", "dark:border-yellow-300", "dark:focus:bg-gray-600", "dark:focus:ring-blue-800", "dark:focus:ring-cyan-800", "dark:focus:ring-gray-600", "dark:focus:ring-gray-700", "dark:focus:ring-gray-800", "dark:focus:ring-green-800", "dark:focus:ring-indigo-800", "dark:focus:ring-lime-800", "dark:focus:ring-pink-800", "dark:focus:ring-primary-800", "dark:focus:ring-purple-800", "dark:focus:ring-red-800", "dark:focus:ring-teal-800", "dark:focus:ring-yellow-900", "dark:focus:text-white", "dark:hover:bg-blue-700", "dark:hover:bg-cyan-700", "dark:hover:bg-gray-600", "dark:hover:bg-gray-700", "dark:hover:bg-green-700", "dark:hover:bg-indigo-700", "dark:hover:bg-lime-700", "dark:hover:bg-pink-700", "dark:hover:bg-primary-700", "dark:hover:bg-purple-700", "dark:hover:bg-red-700", "dark:hover:bg-teal-700", "dark:hover:bg-yellow-400", "dark:hover:border-blue-700", "dark:hover:border-cyan-700", "dark:hover:border-gray-600", "dark:hover:border-gray-700", "dark:hover:border-green-700", "dark:hover:border-indigo-700", "dark:hover:border-lime-700", "dark:hover:border-pink-700", "dark:hover:border-primary-700", "dark:hover:border-purple-700", "dark:hover:border-red-700", "dark:hover:border-teal-700", "dark:hover:border-yellow-400", "dark:hover:text-white", "dark:ring-cyan-800", "dark:ring-gray-400", "dark:ring-gray-500", "dark:ring-gray-800", "dark:ring-green-500", "dark:ring-pink-500", "dark:ring-purple-600", "dark:ring-red-700", "dark:ring-yellow-500", "dark:text-blue-500", "dark:text-cyan-500", "dark:text-gray-200", "dark:text-gray-300", "dark:text-gray-400", "dark:text-gray-600", "dark:text-green-500", "dark:text-indigo-400", "dark:text-lime-500", "dark:text-pink-500", "dark:text-primary-500", "dark:text-purple-400", "dark:text-red-500", "dark:text-teal-400", "dark:text-white", "dark:text-yellow-300", "divide-gray-100", "divide-y", "first:border-l", "first:rounded-s-lg", "flex", "flex-col", "flex-wrap", "focus:bg-gray-100", "focus:outline-none", "focus:ring-2", "focus:ring-4", "focus:ring-blue-300", "focus:ring-cyan-300", "focus:ring-gray-100", "focus:ring-gray-200", "focus:ring-gray-300", "focus:ring-green-300", "focus:ring-indigo-300", "focus:ring-lime-300", "focus:ring-pink-300", "focus:ring-primary-300", "focus:ring-purple-300", "focus:ring-red-300", "focus:ring-teal-300", "focus:ring-yellow-300", "font-medium", "font-semibold", "gap-4", "h-10", "h-12", "h-2", "h-20", "h-3.5", "h-36", "h-4", "h-5", "h-6", "h-8", "h-9", "h-96", "h-[52px]", "h-auto", "h-full", "h-px", "hidden", "hover:bg-blue-800", "hover:bg-cyan-800", "hover:bg-gray-100", "hover:bg-gray-50", "hover:bg-gray-600", "hover:bg-gray-800", "hover:bg-gray-900", "hover:bg-green-800", "hover:bg-indigo-800", "hover:bg-lime-800", "hover:bg-pink-800", "hover:bg-primary-800", "hover:bg-purple-800", "hover:bg-red-800", "hover:bg-teal-800", "hover:bg-yellow-500", "hover:border-blue-800", "hover:border-cyan-800", "hover:border-gray-800", "hover:border-gray-900", "hover:border-green-800", "hover:border-indigo-800", "hover:border-lime-800", "hover:border-pink-800", "hover:border-primary-800", "hover:border-purple-800", "hover:border-red-800", "hover:border-teal-800", "hover:border-yellow-500", "hover:cursor-not-allowed", "hover:text-primary-700", "hover:text-white", "hover:underline", "inline-flex", "invisible", "items-center", "justify-between", "justify-center", "justify-start", "last:mr-0", "last:rounded-e-lg", "lg:my-8", "mb-4", "mb-6", "md:bg-transparent", "md:block", "md:border-0", "md:dark:hover:bg-transparent", "md:dark:hover:text-white", "md:flex", "md:flex-row", "md:font-medium", "md:h-auto", "md:hidden", "md:hover:bg-transparent", "md:hover:text-primary-700", "md:items-center", "md:justify-between", "md:max-w-xl", "md:mr-6", "md:mt-0", "md:p-0", "md:rounded-l-lg", "md:rounded-none", "md:space-x-8", "md:text-primary-700", "md:text-sm", "md:w-48", "md:w-auto", "me-4", "ml-1", "ml-2", "mr-2", "mr-3", "mt-4", "mx-auto", "my-1", "my-6", "object-cover", "opacity-0", "opacity-50", "overflow-hidden", "p-1", "p-2", "p-6", "pl-3", "pointer-events-none", "pr-4", "px-2", "px-3", "px-4", "px-5", "px-6", "py-1", "py-2", "py-2.5", "relative", "ring-2", "ring-cyan-400", "ring-gray-300", "ring-gray-500", "ring-gray-800", "ring-green-500", "ring-pink-500", "ring-purple-500", "ring-red-500", "ring-yellow-300", "rotate-45", "rounded-full", "rounded-lg", "rounded-md", "rounded-none", "rounded-sm", "rounded-t-lg", "self-center", "shadow-md", "shadow-sm", "shadow-xs", "shrink-0", "sm:mb-0", "sm:mx-auto", "sm:px-4", "sm:text-center", "space-x-4", "space-y-4", "sr-only", "text-2xl", "text-base", "text-blue-700", "text-center", "text-cyan-700", "text-gray-400", "text-gray-500", "text-gray-600", "text-gray-700", "text-gray-800", "text-gray-900", "text-green-700", "text-indigo-700", "text-lime-700", "text-pink-700", "text-primary-700", "text-purple-700", "text-red-700", "text-sm", "text-teal-700", "text-white", "text-xs", "text-yellow-400", "top-center", "top-left", "top-right", "transition-opacity", "uppercase", "w-10", "w-2", "w-20", "w-3.5", "w-36", "w-4", "w-5", "w-6", "w-8", "w-auto", "w-fit", "w-full", "whitespace-nowrap", "z-10"]
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float, <PERSON><PERSON><PERSON>, CheckConstraint
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class OrderItem(Base):
    __tablename__ = 'order_items'

    id = Column(Integer, primary_key=True, index=True)
    quantity = Column(Integer, nullable=False, default=1)
    price = Column(Float, nullable=False) # Price at the time of order

    # Foreign Keys
    order_id = Column(Integer, ForeignKey('orders.id'), nullable=False)
    menu_item_id = Column(Integer, ForeignKey('menu_items.id'), nullable=True)
    menu_id = Column(Integer, ForeignKey('menus.id'), nullable=True)

    # Relationships
    order = relationship("Order", back_populates="items")
    menu_item = relationship("MenuItem", back_populates="order_items")
    menu = relationship("Menu", back_populates="order_items")

    # Ensure that either menu_item_id or menu_id is set, but not both
    __table_args__ = (
        CheckConstraint(
            '(menu_item_id IS NOT NULL AND menu_id IS NULL) OR (menu_item_id IS NULL AND menu_id IS NOT NULL)',
            name='check_order_item_type'
        ),
    )

    def __repr__(self):
        item_type = "MenuItem" if self.menu_item_id else "Menu"
        item_id = self.menu_item_id if self.menu_item_id else self.menu_id
        return f"<OrderItem(id={self.id}, type='{item_type}', item_id={item_id})>"

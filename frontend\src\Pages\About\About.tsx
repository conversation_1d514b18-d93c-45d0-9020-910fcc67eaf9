import { Card } from "flowbite-react";

const About = () => {
  return (
    <div className="bg-gray-100 dark:bg-gray-800 py-12">
      <div className="container mx-auto px-4">
        {/* Restaurant Story */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4">Our Story</h2>
          <p className="text-lg text-gray-700 dark:text-gray-400 max-w-3xl mx-auto">
            Our restaurant was born from a passion for authentic Turkish cuisine. We started as a small family business with a dream of sharing the rich flavors of our homeland with the world. Today, we are proud to be a beloved local eatery, known for our delicious doner, kebab, salads, and drinks, all made with the freshest ingredients and a lot of love.
          </p>
        </div>

        {/* Our Chefs */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8">Meet Our Chefs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card
              imgAlt="Chef 1"
              imgSrc="https://via.placeholder.com/300x300?text=Chef+Ahmet"
            >
              <h5 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                Ahmet Yilmaz
              </h5>
              <p className="font-normal text-gray-700 dark:text-gray-400">
                Head Chef
              </p>
            </Card>
            <Card
              imgAlt="Chef 2"
              imgSrc="https://via.placeholder.com/300x300?text=Chef+Fatma"
            >
              <h5 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                Fatma Kaya
              </h5>
              <p className="font-normal text-gray-700 dark:text-gray-400">
                Sous Chef
              </p>
            </Card>
            <Card
              imgAlt="Chef 3"
              imgSrc="https://via.placeholder.com/300x300?text=Chef+Mehmet"
            >
              <h5 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                Mehmet Ozdemir
              </h5>
              <p className="font-normal text-gray-700 dark:text-gray-400">
                Pastry Chef
              </p>
            </Card>
          </div>
        </div>

        {/* Location */}
        <div>
          <h2 className="text-3xl font-bold text-center mb-8">Our Location</h2>
          <div className="w-full h-96">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3010.264880235815!2d28.9744823153942!3d41.0190329792999!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14cab9be9a6e3e9d%3A0x44c7c5d4a6a0e1b!2sHagia%20Sophia!5e0!3m2!1sen!2str!4v1626273136515!5m2!1sen!2str"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen={true}
              loading="lazy"
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
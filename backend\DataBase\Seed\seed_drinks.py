
from sqlalchemy.orm import Session
from ...Models.Drinks.DrinksModel import Drink
from ...Models.Category.CategoryModel import Category

def seed_drinks(db: Session):
    drinks_to_seed = [
        {
            "name": "Coca-Cola",
            "description": "Classic Coca-Cola in a can.",
            "price": 2.50,
            "size": "330ml",
            "is_alcoholic": False,
            "is_carbonated": True,
            "image_url": "/images/drinks/coca_cola.jpg",
            "is_available": True,
            "category_name": "Drinks"
        },
        {
            "name": "Ayran",
            "description": "A refreshing Turkish yogurt drink.",
            "price": 2.00,
            "size": "250ml",
            "is_alcoholic": False,
            "is_carbonated": False,
            "image_url": "/images/drinks/ayran.jpg",
            "is_available": True,
            "category_name": "Drinks"
        },
        {
            "name": "Fanta",
            "description": "Orange flavored carbonated drink.",
            "price": 2.50,
            "size": "330ml",
            "is_alcoholic": False,
            "is_carbonated": True,
            "image_url": "/images/drinks/fanta.jpg",
            "is_available": True,
            "category_name": "Drinks"
        },
        {
            "name": "Sprite",
            "description": "Lemon-lime flavored carbonated drink.",
            "price": 2.50,
            "size": "330ml",
            "is_alcoholic": False,
            "is_carbonated": True,
            "image_url": "/images/drinks/sprite.jpg",
            "is_available": True,
            "category_name": "Drinks"
        },
        {
            "name": "Still Water",
            "description": "A bottle of still mineral water.",
            "price": 1.50,
            "size": "500ml",
            "is_alcoholic": False,
            "is_carbonated": False,
            "image_url": "/images/drinks/water.jpg",
            "is_available": True,
            "category_name": "Drinks"
        },
        {
            "name": "Sparkling Water",
            "description": "A bottle of sparkling mineral water.",
            "price": 1.75,
            "size": "500ml",
            "is_alcoholic": False,
            "is_carbonated": True,
            "image_url": "/images/drinks/sparkling_water.jpg",
            "is_available": True,
            "category_name": "Drinks"
        },
        {
            "name": "Şalgam",
            "description": "A traditional Turkish drink made from fermented black carrots.",
            "price": 2.25,
            "size": "300ml",
            "is_alcoholic": False,
            "is_carbonated": False,
            "image_url": "/images/drinks/salgam.jpg",
            "is_available": True,
            "category_name": "Drinks"
        }
    ]

    for drink_data in drinks_to_seed:
        drink_exists = db.query(Drink).filter(Drink.name == drink_data["name"]).first()
        if not drink_exists:
            category = db.query(Category).filter(Category.name == drink_data["category_name"]).first()
            if not category:
                # If the category doesn't exist, you might want to create it
                # For now, we'll just skip the drink if the category isn't found
                continue

            new_drink = Drink(
                name=drink_data["name"],
                description=drink_data["description"],
                price=drink_data["price"],
                size=drink_data["size"],
                is_alcoholic=drink_data["is_alcoholic"],
                is_carbonated=drink_data["is_carbonated"],
                image_url=drink_data["image_url"],
                is_available=drink_data["is_available"],
                category_id=category.id
            )
            db.add(new_drink)

    db.commit()

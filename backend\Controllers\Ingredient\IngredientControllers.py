from fastapi import HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Ingredients.IngredientsModel import Ingredient as IngredientModel
from ...Schemas.IngredientSchemas.IngredientSchemas import Ingredient, IngredientCreate, IngredientUpdate


class IngredientControllers:
    
    @staticmethod
    async def get_ingredient(db: Session, ingredient_id: int) -> IngredientModel:
        """Get a single ingredient by ID"""
        ingredient = db.query(IngredientModel).filter(IngredientModel.id == ingredient_id).first()
        if not ingredient:
            raise HTTPException(status_code=404, detail="Ingredient not found")
        return ingredient
    
    @staticmethod
    async def get_ingredients(db: Session, skip: int = 0, limit: int = 100) -> List[IngredientModel]:
        """Get all ingredients with pagination"""
        return db.query(IngredientModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_ingredient(db: Session, ingredient: IngredientCreate) -> IngredientModel:
        """Create a new ingredient"""
        db_ingredient = IngredientModel(**ingredient.model_dump())
        db.add(db_ingredient)
        db.commit()
        db.refresh(db_ingredient)
        return db_ingredient
    
    @staticmethod
    async def update_ingredient(db: Session, ingredient_id: int, ingredient: IngredientUpdate) -> IngredientModel:
        """Update an existing ingredient"""
        db_ingredient = db.query(IngredientModel).filter(IngredientModel.id == ingredient_id).first()
        if not db_ingredient:
            raise HTTPException(status_code=404, detail="Ingredient not found")
        
        update_data = ingredient.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_ingredient, key, value)
        
        db.commit()
        db.refresh(db_ingredient)
        return db_ingredient
    
    @staticmethod
    async def delete_ingredient(db: Session, ingredient_id: int) -> IngredientModel:
        """Delete an ingredient"""
        db_ingredient = db.query(IngredientModel).filter(IngredientModel.id == ingredient_id).first()
        if not db_ingredient:
            raise HTTPException(status_code=404, detail="Ingredient not found")
        
        db.delete(db_ingredient)
        db.commit()
        return db_ingredient

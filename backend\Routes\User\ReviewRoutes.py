from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.User.ReviewControllers import ReviewControllers
from ...DataBase.DataBase import get_db
from ...Schemas.UserSchemas.ReviewSchemas import Review, ReviewCreate, ReviewUpdate

Review_Router = APIRouter(
    prefix="/reviews",
    tags=["reviews"],
)

# Create Review
@Review_Router.post("/", response_model=Review)
async def create_review_route(review: ReviewCreate, db: Session = Depends(get_db)):
    return await ReviewControllers.create_review(db, review)

# Get All Reviews
@Review_Router.get("/", response_model=List[Review])
async def read_reviews_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await ReviewControllers.get_reviews(db, skip, limit)

# Get Single Review
@Review_Router.get("/{review_id}", response_model=Review)
async def read_review_route(review_id: int, db: Session = Depends(get_db)):
    return await ReviewControllers.get_review(db, review_id)

# Update Review
@Review_Router.put("/{review_id}", response_model=Review)
async def update_review_route(review_id: int, review: ReviewUpdate, db: Session = Depends(get_db)):
    return await ReviewControllers.update_review(db, review_id, review)

# Delete Review
@Review_Router.delete("/{review_id}", response_model=Review)
async def delete_review_route(review_id: int, db: Session = Depends(get_db)):
    return await ReviewControllers.delete_review(db, review_id)

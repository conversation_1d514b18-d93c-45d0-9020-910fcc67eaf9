
from sqlalchemy.orm import Session
from ...Models.Kebabs.KebabModel import Ke<PERSON><PERSON>
from ...Models.Ingredients.IngredientsModel import Ingredient
from ...Models.Category.CategoryModel import Category

def seed_kebabs(db: Session):
    kebabs_to_seed = [
        {
            "name": "Adana Kebab",
            "description": "Spicy minced lamb kebab mounted on a wide iron skewer and grilled.",
            "price": 18.00,
            "meat_type": "Lamb",
            "spice_level": 4,
            "image_url": "/images/kebabs/adana_kebab.jpg",
            "is_available": True,
            "category_name": "Kebab",
            "ingredients": ["Ground Lamb", "Chili Flakes", "Bell Pepper", "Parsley", "Pita Bread"]
        },
        {
            "name": "Chicken Shish Kebab",
            "description": "Cubes of marinated chicken threaded onto a skewer and grilled to perfection.",
            "price": 16.50,
            "meat_type": "Chicken",
            "spice_level": 2,
            "image_url": "/images/kebabs/chicken_shish.jpg",
            "is_available": True,
            "category_name": "Kebab",
            "ingredients": ["Chicken Breast", "Olive Oil", "Lemon", "Yogurt Sauce", "Bell Pepper", "Onion"]
        },
        {
            "name": "Beef Urfa Kebab",
            "description": "A milder version of the Adana kebab, made with ground beef and seasoned with paprika.",
            "price": 17.50,
            "meat_type": "Beef",
            "spice_level": 2,
            "image_url": "/images/kebabs/urfa_kebab.jpg",
            "is_available": True,
            "category_name": "Kebab",
            "ingredients": ["Ground Beef", "Paprika", "Onion", "Parsley", "Pita Bread"]
        },
        {
            "name": "Iskender Kebab",
            "description": "Doner kebab meat served over pide bread, topped with hot tomato sauce and melted butter.",
            "price": 20.00,
            "meat_type": "Lamb",
            "spice_level": 1,
            "image_url": "/images/kebabs/iskender_kebab.jpg",
            "is_available": True,
            "category_name": "Kebab",
            "ingredients": ["Lamb", "Pita Bread", "Tomato", "Yogurt Sauce", "Olive Oil"]
        },
        {
            "name": "Vegetable Kebab",
            "description": "A delicious mix of grilled vegetables for a healthy and flavorful option.",
            "price": 14.00,
            "meat_type": "Vegetarian",
            "spice_level": 1,
            "image_url": "/images/kebabs/vegetable_kebab.jpg",
            "is_available": True,
            "category_name": "Kebab",
            "ingredients": ["Bell Pepper", "Onion", "Tomato", "Mushrooms", "Corn", "Olive Oil", "Oregano"]
        }
    ]

    for kebab_data in kebabs_to_seed:
        kebab_exists = db.query(Kebab).filter(Kebab.name == kebab_data["name"]).first()
        if not kebab_exists:
            category = db.query(Category).filter(Category.name == kebab_data["category_name"]).first()
            if not category:
                continue

            new_kebab = Kebab(
                name=kebab_data["name"],
                description=kebab_data["description"],
                price=kebab_data["price"],
                meat_type=kebab_data["meat_type"],
                spice_level=kebab_data["spice_level"],
                image_url=kebab_data["image_url"],
                is_available=kebab_data["is_available"],
                category_id=category.id
            )

            ingredients = db.query(Ingredient).filter(Ingredient.name.in_(kebab_data["ingredients"])).all()
            new_kebab.ingredients.extend(ingredients)

            db.add(new_kebab)

    db.commit()

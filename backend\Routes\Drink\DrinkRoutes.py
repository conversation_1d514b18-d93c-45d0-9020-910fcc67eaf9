from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Drink.DrinkControllers import DrinkControllers
from ...DataBase.DataBase import get_db
from ...Schemas.DrinkSchemas.DrinkSchemas import Drink, DrinkCreate, DrinkUpdate

Drink_Router = APIRouter(
    prefix="/drinks",
    tags=["drinks"],
)

# Create Drink
@Drink_Router.post("/", response_model=Drink)
async def create_drink_route(drink: DrinkCreate, db: Session = Depends(get_db)):
    return await DrinkControllers.create_drink(db, drink)

# Get All Drinks
@Drink_Router.get("/", response_model=List[Drink])
async def read_drinks_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await DrinkControllers.get_drinks(db, skip, limit)

# Get Single Drink
@Drink_Router.get("/{drink_id}", response_model=Drink)
async def read_drink_route(drink_id: int, db: Session = Depends(get_db)):
    return await DrinkControllers.get_drink(db, drink_id)

# Update Drink
@Drink_Router.put("/{drink_id}", response_model=Drink)
async def update_drink_route(drink_id: int, drink: DrinkUpdate, db: Session = Depends(get_db)):
    return await DrinkControllers.update_drink(db, drink_id, drink)

# Delete Drink
@Drink_Router.delete("/{drink_id}", response_model=Drink)
async def delete_drink_route(drink_id: int, db: Session = Depends(get_db)):
    return await DrinkControllers.delete_drink(db, drink_id)
from pydantic import BaseModel, Field
from typing import Optional

class OrderItemBase(BaseModel):
    quantity: int = Field(..., gt=0)
    price: float

class OrderItemCreate(OrderItemBase):
    order_id: int
    menu_item_id: Optional[int] = None
    menu_id: Optional[int] = None

class OrderItemUpdate(BaseModel):
    quantity: Optional[int] = Field(None, gt=0)

class OrderItem(OrderItemBase):
    id: int
    order_id: int
    menu_item_id: Optional[int] = None
    menu_id: Optional[int] = None

    class Config:
        from_attributes = True

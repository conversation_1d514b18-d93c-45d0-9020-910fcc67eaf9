from sqlalchemy import Column, Integer, DateTime, ForeignKey, func, CheckConstraint
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class Review(Base):
    __tablename__ = 'reviews'

    id = Column(Integer, primary_key=True, index=True)
    rating = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=func.now())

    # Foreign Keys
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    menu_item_id = Column(Integer, ForeignKey('menu_items.id'), nullable=False)

    # Relationships
    user = relationship("User", back_populates="reviews")
    menu_item = relationship("MenuItem", back_populates="reviews")

    # Add a constraint to ensure rating is between 1 and 5
    __table_args__ = (CheckConstraint('rating >= 1 AND rating <= 5', name='rating_check'),)

    def __repr__(self):
        return f"<Review(id={self.id}, rating={self.rating})>"

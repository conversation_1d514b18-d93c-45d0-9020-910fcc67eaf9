from fastapi import HTTPEx<PERSON>
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Doners.DonerModel import Doner as DonerModel
from ...Schemas.DonerSchemas.DonerSchemas import Doner, Doner<PERSON><PERSON>, DonerUpdate


class DonerControllers:
    
    @staticmethod
    async def get_doner(db: Session, doner_id: int) -> DonerModel:
        """Get a single doner by ID"""
        doner = db.query(DonerModel).filter(DonerModel.id == doner_id).first()
        if not doner:
            raise HTTPException(status_code=404, detail="Doner not found")
        return doner
    
    @staticmethod
    async def get_doners(db: Session, skip: int = 0, limit: int = 100) -> List[DonerModel]:
        """Get all doners with pagination"""
        return db.query(DonerModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_doner(db: Session, doner: DonerCreate) -> DonerModel:
        """Create a new doner"""
        db_doner = DonerModel(**doner.model_dump())
        db.add(db_doner)
        db.commit()
        db.refresh(db_doner)
        return db_doner
    
    @staticmethod
    async def update_doner(db: Session, doner_id: int, doner: DonerUpdate) -> DonerModel:
        """Update an existing doner"""
        db_doner = db.query(DonerModel).filter(DonerModel.id == doner_id).first()
        if not db_doner:
            raise HTTPException(status_code=404, detail="Doner not found")
        
        update_data = doner.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_doner, key, value)
        
        db.commit()
        db.refresh(db_doner)
        return db_doner
    
    @staticmethod
    async def delete_doner(db: Session, doner_id: int) -> DonerModel:
        """Delete a doner"""
        db_doner = db.query(DonerModel).filter(DonerModel.id == doner_id).first()
        if not db_doner:
            raise HTTPException(status_code=404, detail="Doner not found")
        
        db.delete(db_doner)
        db.commit()
        return db_doner

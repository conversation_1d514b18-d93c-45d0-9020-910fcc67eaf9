from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Salad.SaladControllers import SaladControllers
from ...DataBase.DataBase import get_db
from ...Schemas.SaladSchemas.SaladSchemas import Salad, SaladCreate, SaladUpdate

Salad_Router = APIRouter(
    prefix="/salads",
    tags=["salads"],
)

# Create Salad
@Salad_Router.post("/", response_model=Salad)
async def create_salad_route(salad: SaladCreate, db: Session = Depends(get_db)):
    return await SaladControllers.create_salad(db, salad)

# Get All Salads
@Salad_Router.get("/", response_model=List[Salad])
async def read_salads_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await SaladControllers.get_salads(db, skip, limit)

# Get Single Salad
@Salad_Router.get("/{salad_id}", response_model=Salad)
async def read_salad_route(salad_id: int, db: Session = Depends(get_db)):
    return await SaladControllers.get_salad(db, salad_id)

# Update Salad
@Salad_Router.put("/{salad_id}", response_model=Salad)
async def update_salad_route(salad_id: int, salad: SaladUpdate, db: Session = Depends(get_db)):
    return await SaladControllers.update_salad(db, salad_id, salad)

# Delete Salad
@Salad_Router.delete("/{salad_id}", response_model=Salad)
async def delete_salad_route(salad_id: int, db: Session = Depends(get_db)):
    return await SaladControllers.delete_salad(db, salad_id)
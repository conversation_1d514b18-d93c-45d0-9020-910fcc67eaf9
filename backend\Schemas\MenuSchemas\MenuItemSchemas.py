from pydantic import BaseModel, Field
from typing import Optional, List
from ..UserSchemas.CommentSchemas import Comment
from ..UserSchemas.ReviewSchemas import Review

class MenuItemBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: float
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: bool = True

class MenuItemCreate(MenuItemBase):
    pass

class MenuItemUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    price: Optional[float] = None
    image_url: Optional[str] = Field(None, max_length=255)
    is_available: Optional[bool] = None

class MenuItem(MenuItemBase):
    id: int
    comments: List[Comment] = []
    reviews: List[Review] = []

    class Config:
        from_attributes = True

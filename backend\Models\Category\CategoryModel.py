from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class Category(Base):
    __tablename__ = 'categories'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)

    # One-to-Many relationships with product models
    doners = relationship("Doner", back_populates="category")
    drinks = relationship("Drink", back_populates="category")
    kebabs = relationship("Kebab", back_populates="category")
    salads = relationship("Salad", back_populates="category")

    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}')>"
